<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fish Eat Fish</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body class="fullscreen">
    <!-- Loading Screen -->
    <div id="loading-screen">
        <div class="loading-content">
            <h1>Fish Eat Fish</h1>
            <div class="loading-bar-container">
                <div class="loading-bar"></div>
            </div>
            <p class="loading-text">Loading game assets...</p>
        </div>
    </div>

    <div id="game-container" class="hidden">
        <!-- Main Menu -->
        <div id="main-menu">
            <h1>Fish Eat Fish</h1>
            <div id="player-name-container">
                <input type="text" id="player-name" placeholder="Enter your name" maxlength="15">
            </div>
            <button id="start-button">Start Game</button>
            <button id="how-to-play-button">How to Play</button>
        </div>

        <!-- How to Play Screen -->
        <div id="how-to-play" class="hidden">
            <h2>How to Play</h2>
            <div class="tutorial-content">
                <div class="tutorial-section">
                    <h3>Basic Controls</h3>
                    <p>Move your fish with the mouse cursor.</p>
                    <p>Eat smaller fish to grow bigger.</p>
                    <p>Avoid larger fish or you'll be eaten!</p>
                </div>



                <div class="tutorial-section">
                    <h3>Leveling Up</h3>
                    <p>Earn points to advance to the next level.</p>
                    <p>Each level increases difficulty.</p>
                    <p>The progress bar at the top shows your progress to the next level.</p>
                </div>
            </div>
            <button id="back-button">Back to Menu</button>
        </div>

        <!-- Tutorial Overlay -->
        <div id="tutorial-overlay" class="hidden">
            <div id="tutorial-box">
                <h3 id="tutorial-title">Welcome to Fish Eat Fish!</h3>
                <p id="tutorial-text">Let's learn how to play the game.</p>
                <div id="tutorial-navigation">
                    <button id="tutorial-prev" class="hidden">Previous</button>
                    <span id="tutorial-step">1/6</span>
                    <button id="tutorial-next">Next</button>
                </div>
            </div>
        </div>

        <!-- Game Canvas -->
        <canvas id="game-canvas" class="hidden"></canvas>

        <!-- Minimap -->
        <div id="minimap-container" class="hidden">
            <canvas id="minimap"></canvas>
        </div>

        <!-- Game HUD removed to avoid overlap with leaderboard -->

        <!-- Progress Bar -->
        <div id="progress-bar-container" class="hidden">
            <div id="level-number">1</div>
            <div id="growth-progress-container">
                <div id="growth-progress-bar"></div>
            </div>
        </div>



        <!-- Game Over Screen -->
        <div id="game-over" class="hidden">
            <h2>Game Over</h2>
            <p id="final-score">Your Score: 0</p>
            <p id="high-score">High Score: 0</p>

            <button id="restart-button">Play Again</button>
            <button id="menu-button">Main Menu</button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/object-pool.js"></script>
    <script src="js/performance-monitor.js"></script>
    <script src="js/water-effect.js"></script>
    <script src="js/food.js"></script>
    <script src="js/powerup.js"></script>
    <script src="js/enemy.js"></script>
    <script src="js/player.js"></script>
    <script src="js/ai-player.js"></script>
    <script src="js/leaderboard.js"></script>
    <script src="js/multiplayer.js"></script>
    <script src="js/game.js"></script>
</body>
</html>
