* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.fullscreen {
    font-family: 'Arial', sans-serif;
    background-color: #004080; /* Match game background */
    overflow: hidden;
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
}

/* Loading Screen Styles */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(to bottom, #4FACFE, #0080DB, #005BBB);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-content h1 {
    font-size: 48px;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    animation: pulse 2s infinite;
}

.loading-bar-container {
    width: 300px;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    margin: 20px auto;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.loading-bar {
    height: 100%;
    width: 0%;
    background: linear-gradient(to right, #33CC33, #FFFF00);
    border-radius: 10px;
    animation: loading 3s ease-in-out forwards;
}

.loading-text {
    font-size: 18px;
    margin-top: 20px;
    opacity: 0.8;
}

@keyframes loading {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

#game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    background-color: #87ceeb;
    overflow: hidden;
}

#game-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    cursor: none; /* Hide default cursor */
}

/* Minimap styling */
#minimap-container {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 200px;
    height: 150px;
    background-color: rgba(0, 30, 60, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 5px;
    z-index: 2;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

#minimap {
    width: 100%;
    height: 100%;
}

#main-menu, #how-to-play, #game-over {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 100, 150, 0.8);
    color: white;
    z-index: 3; /* Increased z-index to appear above minimap */
}

#food-chain-info {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 10px;
    margin: 15px 0;
    text-align: center;
}

#food-chain-info p {
    margin: 8px 0;
    font-size: 16px;
}

#preferred-prey, #weakness {
    font-weight: bold;
    font-size: 18px;
}

#high-score {
    font-size: 18px;
    margin-bottom: 10px;
    color: #FFD700;
}

h1 {
    font-size: 48px;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

h2 {
    font-size: 36px;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

button {
    background-color: #ff9900;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    margin: 10px;
    font-size: 18px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #ffaa33;
}

#player-name-container {
    margin: 15px 0;
}

#player-name {
    padding: 10px;
    font-size: 18px;
    border: none;
    border-radius: 5px;
    width: 250px;
    text-align: center;
}

#high-scores {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 100, 150, 0.8);
    color: white;
    z-index: 3;
}

#high-scores-list {
    background-color: rgba(0, 0, 0, 0.5);
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    width: 80%;
    max-width: 500px;
    max-height: 400px;
    overflow-y: auto;
}

.high-score-entry {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.high-score-entry:last-child {
    border-bottom: none;
}

.high-score-rank {
    width: 40px;
}

.high-score-name {
    flex-grow: 1;
    text-align: left;
    margin: 0 10px;
}

.high-score-score {
    width: 80px;
    text-align: right;
}

/* Game HUD removed to avoid overlap with leaderboard */

#progress-bar-container {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 5px;
    width: 300px;
    display: flex;
    align-items: center;
    pointer-events: none; /* Allow clicking through the container */
}

#level-number {
    color: white;
    font-size: 18px;
    font-weight: bold;
    margin-right: 10px;
    min-width: 20px;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}





#size-level, #level-display {
    margin-bottom: 8px;
}

#growth-progress-container {
    flex: 1;
    height: 12px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    overflow: hidden;
}

#growth-progress-bar {
    height: 100%;
    background-color: #33CC33; /* Green by default */
    width: 0%;
    transition: width 0.2s ease-out, background-color 0.3s;
}



#progress-container {
    width: 100%;
    height: 10px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    overflow: hidden;
    margin-top: 5px;
}

#progress-bar {
    height: 100%;
    background-color: #FF9900;
    width: 0%;
    transition: width 0.3s ease-out;
}

/* Tutorial Styles */
.tutorial-content {
    max-height: 400px;
    overflow-y: auto;
    width: 80%;
    margin: 0 auto;
    padding: 10px;
}

.tutorial-section {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
}

.tutorial-section h3 {
    margin-top: 0;
    color: #FFD700;
    font-size: 24px;
    margin-bottom: 10px;
}

.tutorial-section ul {
    list-style-type: none;
    padding-left: 10px;
}

.tutorial-section li {
    margin-bottom: 8px;
}

.fish-type, .power-up {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 5px;
    font-weight: bold;
    margin-right: 5px;
}

.fish-type.golden {
    background-color: #FFD700;
    color: black;
}

.fish-type.poisonous {
    background-color: #7FFF00;
    color: black;
}

.fish-type.speedy {
    background-color: #00FFFF;
    color: black;
}

.fish-type.armored {
    background-color: #A9A9A9;
    color: white;
}

.power-up {
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    font-size: 20px;
}

.power-up.speed {
    background-color: #FFFF00;
    color: black;
}

.power-up.invincibility {
    background-color: #FFD700;
    color: black;
}

.power-up.double-points {
    background-color: #FF00FF;
    color: white;
}

.power-up.magnetism {
    background-color: #00BFFF;
    color: white;
}

.power-up.frenzy {
    background-color: #00FF00;
    color: black;
}

/* In-game Tutorial Overlay */
#tutorial-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: auto; /* Ensure tutorial overlay captures clicks */
}

#tutorial-box {
    background-color: rgba(0, 100, 150, 0.9);
    padding: 20px;
    border-radius: 10px;
    width: 80%;
    max-width: 600px;
    text-align: center;
    color: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

#tutorial-title {
    color: #FFD700;
    font-size: 24px;
    margin-top: 0;
}

#tutorial-text {
    font-size: 18px;
    margin: 20px 0;
    line-height: 1.5;
}

#tutorial-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

#tutorial-step {
    font-size: 16px;
    color: #FFD700;
}

.hidden {
    display: none !important;
}
